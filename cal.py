# Current status
bank_debt = -**********  # 2.2 billion
bank_saving = 2******** # 250 million

# Goals
pay_debts_goal = **********  # 2.2 billion - pay debts
buy_new_house_goal = ***********  # 17.91 billion
buy_new_car_goal = **********  # 3.7 billion
FI_goal = ***********  # 26.96 billion

milestone_1 = 5  # 5 years
milestone_2 = 10  # 10 years
milestone_3 = 15  # 15 years

roi_avg = 0.1  # 10%

# For the first 5 years, focus on paying off debts
pay_debts_yearly = [0]*15
added_investment_FI_yearly = [0] * 15
added_investment_buy_new_house_yearly = [0] * 15
added_investment_buy_new_car_yearly = [0] * 15

total_value_FI_yearly = [0] * 15
total_value_buy_new_house_yearly = [0] * 15
total_value_buy_new_car_yearly = [0] * 15

start_amount_0 = *********  # 200 million
investment_increase_rate_first_5_years = 0.2  # 20%

# Goal #1: Pay off debts
pay_debts_start_amount = *********  # 300 million
FI_start_amount = ********  # 50 million
bank_debt_remaining = [0] * 15
bank_debt_remaining[0] = bank_debt
for i in range(0,5):
    pay_debts_yearly[i] = pay_debts_start_amount * ((1 + investment_increase_rate_first_5_years) ** i)
    bank_debt_remaining[i] = bank_debt_remaining[i-1] + pay_debts_yearly[i]  if i > 0 else bank_debt_remaining[i] + pay_debts_yearly[i]
    added_investment_FI_yearly[i] = FI_start_amount * ((1 + investment_increase_rate_first_5_years) ** i)
    total_value_FI_yearly[i] = total_value_FI_yearly[i-1] * (1 + roi_avg) + added_investment_FI_yearly[i] if i > 0 else total_value_FI_yearly[i] + added_investment_FI_yearly[i]

# Summary of 1st 5 years
print("===== SUMMARY OF 1ST 5 YEARS =====")
print(f"pay_debts_yearly: {pay_debts_yearly}")
print(f"bank_debt_remaining: {bank_debt_remaining}")
print(f"added_investment_FI_yearly: {added_investment_FI_yearly}")
print(f"total_value_FI_yearly: {total_value_FI_yearly}")

# Goals #2 and #3: Buy a new house and a new car
investment_increase_rate_second_5_years = 0.3  # 30%

# Invest for buying house and buying car with ration 80/20
buy_new_house_start_amount = pay_debts_yearly[4] * (1 + investment_increase_rate_second_5_years) * 0.8
buy_new_car_start_amount = pay_debts_yearly[4] * (1 + investment_increase_rate_second_5_years) * 0.2
FI_start_amount_second_5_years = added_investment_FI_yearly[4] * (1 + investment_increase_rate_second_5_years)
for i in range(5,10):
    added_investment_buy_new_house_yearly[i] = buy_new_house_start_amount * ((1 + investment_increase_rate_second_5_years) ** (i-5))
    total_value_buy_new_house_yearly[i] = total_value_buy_new_house_yearly[i-1] * (1 + roi_avg) + added_investment_buy_new_house_yearly[i]
    added_investment_buy_new_car_yearly[i] = buy_new_car_start_amount * ((1 + investment_increase_rate_second_5_years) ** (i-5))
    total_value_buy_new_car_yearly[i] = total_value_buy_new_car_yearly[i-1] * (1 + roi_avg) + added_investment_buy_new_car_yearly[i]
    added_investment_FI_yearly[i] = FI_start_amount_second_5_years * ((1 + investment_increase_rate_second_5_years) ** (i-5))
    total_value_FI_yearly[i] = total_value_FI_yearly[i-1] * (1 + roi_avg) + added_investment_FI_yearly[i]

# Summary of 2nd 5 years
print("===== SUMMARY OF 2ND 5 YEARS =====")
print(f"added_investment_buy_new_house_yearly: {added_investment_buy_new_house_yearly}")
print(f"total_value_buy_new_house_yearly: {total_value_buy_new_house_yearly}")
print(f"added_investment_buy_new_car_yearly: {added_investment_buy_new_car_yearly}")
print(f"total_value_buy_new_car_yearly: {total_value_buy_new_car_yearly}")
print(f"added_investment_FI_yearly: {added_investment_FI_yearly}")
print(f"total_value_FI_yearly: {total_value_FI_yearly}")

# Goal #4: FI
investment_increase_rate_third_5_years = 0.4  # 40%
FI_start_amount_third_5_years = (added_investment_buy_new_house_yearly[9] + added_investment_buy_new_car_yearly[9] + added_investment_FI_yearly[9]) * (1 + investment_increase_rate_third_5_years)
for i in range(10,15):
    added_investment_FI_yearly[i] = FI_start_amount_third_5_years * ((1 + investment_increase_rate_third_5_years) ** (i-10))
    total_value_FI_yearly[i] = total_value_FI_yearly[i-1] * (1 + roi_avg) + added_investment_FI_yearly[i]

# Summary of 3rd 5 years
print("===== SUMMARY OF 3RD 5 YEARS =====")
print(f"added_investment_FI_yearly: {added_investment_FI_yearly}")
print(f"total_value_FI_yearly: {total_value_FI_yearly}")